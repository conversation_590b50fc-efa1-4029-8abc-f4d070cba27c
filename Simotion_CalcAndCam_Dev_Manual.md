# Simotion纺织机械 - 工艺参数计算与凸轮生成模块开发说明书

## 1. 模块概述

### 1.1. 模块目的与核心功能简介

本模块（后续简称“计算与凸轮模块”）是Simotion纺织机械控制系统的核心计算引擎。其主要目的在于根据操作员设定的工艺要求和机器的几何特性，精确计算出生产过程中所需的各种工艺参数，并动态生成或选择合适的凸轮曲线，以驱动相关机械部件（如成形导纱器、锭子等）实现高质量的纱线加工和纱穗成型。

**核心功能包括：**

*   **纱穗成型几何建模与参数计算**: 模拟纱线在纱管上逐层卷绕的过程，计算不同卷绕阶段的纱穗直径、体积、所需圈数等。
*   **运动参数计算**: 根据工艺要求（如捻度、产量、纱线支数）和机器特性，计算各主要运动轴（走车、锭子、罗拉、滚筒等）的目标速度、加速度、Jerk值以及运动时间。
*   **动态凸轮曲线生成**:
    *   为成形轴（如导纱器/横动导轨）生成精确的凸轮曲线 (`Cam_1`)，以控制纱线在纱穗上的绫交叉排列，形成特定形状的纱穗（如筒子纱、宝塔纱）。
    *   为锭子轴生成退绕凸轮曲线 (`Cam_3`)，用于在特定工序（如落纱前的退绕）中精确控制锭子的反向运动。
    *   根据张力测定数据生成或选择张力补偿凸轮曲线 (`Cam_2`)，用于优化卷绕过程中的张力控制。
*   **工艺参数的动态调整**: 能够响应HMI设定的工艺参数变化，或根据机器运行状态（如当前卷绕周期、张力反馈）动态调整计算结果和凸轮参数。

### 1.2. 模块在整体系统中的定位与依赖关系

计算与凸轮模块是整个控制系统的“大脑”之一，处于承上启下的关键位置：

*   **上游依赖**:
    *   **HMI模块**: 接收操作员输入的工艺参数（如纱线支数、目标捻度、纱穗尺寸、机器速度设定等）。
    *   **主控制与状态管理模块**: 接收当前机器运行模式、状态以及特定工艺阶段的触发信号。
    *   **传感器/轴反馈**: 间接依赖轴的实际位置和速度反馈（例如，张力控制模块可能会使用这些反馈，并将调整信号传递给本模块以影响锭子速度计算）。
*   **下游影响**:
    *   **轴运动控制接口模块 (TO程序)**: 本模块计算出的目标位置、速度、加速度、Jerk值以及生成的凸轮曲线数据点，将作为输入参数传递给各个轴的TO程序，驱动物理轴运动。
    *   **主控制与状态管理模块**: 本模块计算出的某些结果（如总卷绕周期数、当前卷绕直径等）可能会反馈给主控模块，用于流程判断和状态转换。
    *   **HMI模块**: 部分计算结果（如预计产量、当前捻度、纱穗成型进度等）会输出到HMI进行显示。

```mermaid
graph LR
    A[HMI模块] --> B(计算与凸轮模块);
    C[主控制与状态管理模块] --> B;
    D[传感器/轴反馈 (间接)] --> B;
    B --> E[轴运动控制接口模块];
    B --> C;
    B --> A;
```

### 1.3. 预期用户与使用场景

*   **预期用户**:
    *   **控制系统开发工程师**: 使用本说明书进行模块的详细设计、编码实现和测试。
    *   **工艺工程师**: 提供工艺参数的设定范围、约束条件以及对纱穗成型质量的要求，并验证计算结果和凸轮曲线的正确性。
    *   **设备操作员/技术员**: 通过HMI间接使用本模块的功能（设定工艺参数，监控生产过程）。
*   **使用场景**:
    *   **生产准备**: 在开始新的生产批次前，根据设定的工艺参数，预计算整个卷绕过程的关键参数和基础凸轮曲线。
    *   **自动运行中**: 在机器自动运行的每个卷绕周期或特定工艺阶段，动态计算或调整运动参数和凸轮曲线，以适应纱穗直径的变化和张力控制的需求。
    *   **工艺参数变更**: 当操作员通过HMI修改工艺参数时，本模块需要重新进行计算并更新相关的控制参数。
    *   **设备标定与优化**: 张力补偿凸轮的生成依赖于张力测定模式下采集的数据，本模块参与此过程。

## 2. 需求规格

### 2.1. 功能需求

#### 2.1.1. 工艺参数计算

##### 输入参数列表 (基于现有代码分析，纺织工艺特定)

| 参数名称 (代码参考)                      | 数据类型 | 单位/范围/约束                                  | 描述                                                                 | 来源 (推测) |
| :--------------------------------------- | :------- | :---------------------------------------------- | :------------------------------------------------------------------- | :---------- |
| **纱线特性**                             |          |                                                 |                                                                      |             |
| 纱线支数 (`HMI_r64ZhiShu_ChengSha`)        | LREAL    | Nm (例如: >0)                                   | 成纱的公制支数                                                           | HMI         |
| 纱线密度/比重 (`HMI_r64_m_u`)            | LREAL    | g/cm³ 或 kg/m³                                  | 用于体积-质量转换                                                        | HMI/固定值  |
| **纱管/筒管参数**                        |          |                                                 |                                                                      |             |
| 纱管底部直径 (`HMI_r64Diameter_ShaGuan_Bottom`) | LREAL    | mm (>0)                                         | 空纱管底部外径                                                         | HMI         |
| 纱管中部直径 (`HMI_r64Diameter_ShaGuan_Middle`) | LREAL    | mm (>0)                                         | 空纱管中部外径（如果是锥形管）                                               | HMI         |
| 纱管顶部直径 (`HMI_r64Diameter_ShaGuan_Top`)   | LREAL    | mm (>0)                                         | 空纱管顶部外径                                                         | HMI         |
| 纱管各段高度 (`HMI_r64_h0_s`, `HMI_r64Height_ShaGuan_Middle`, `HMI_r64Height_ShaGuan_Top`) | LREAL[]  | mm (>=0)                                        | 纱管各锥段或柱段的高度                                                     | HMI         |
| **目标纱穗参数**                         |          |                                                 |                                                                      |             |
| 纱穗最大直径 (`HMI_r64Diameter_ShaSui_Max`)  | LREAL    | mm (>纱管直径)                                  | 成形后纱穗的最大外径                                                       | HMI         |
| 纱穗总高度/升程 (`HMI_r64Height_ShaSui_whole`) | LREAL    | mm (>0)                                         | 纱线在纱管上卷绕的总高度                                                     | HMI         |
| 纱穗底部锥高 (`HMI_r64Height_ShaSui_Bottom`) | LREAL    | mm (>=0)                                        | 纱穗底部锥形部分的高度                                                       | HMI         |
| 纱穗顶部锥高 (`HMI_r64Height_ShaSui_Top`)    | LREAL    | mm (>=0)                                        | 纱穗顶部锥形部分的高度                                                       | HMI         |
| 纱穗起始卷绕高度 (`HMI_r64Height_ShaSui_Start`) | LREAL    | mm (>=0)                                        | 纱线开始在纱管上卷绕的起始高度（相对于纱管底部）                                   | HMI         |
| **机器与工艺设定**                       |          |                                                 |                                                                      |             |
| 目标捻度 (`HMI_r64NianDu_all`)             | LREAL    | TPM (捻/米)                                     | 纱线的最终捻度                                                           | HMI         |
| 追捻捻度/分段捻度 (`HMI_r64NianDu_1`, `HMI_r64NianSu_2`) | LREAL    | TPM                                             | 用于实现分段加捻或追捻工艺的参数                                               | HMI         |
| 捻缩参数 (`HMI_r64Position_NianSuo`, `HMI_r64NianDu_NianSuo`) | LREAL    | mm, TPM                                         | 捻缩发生的行程位置和对应的捻度                                                 | HMI         |
| 出车行程/展距 (`HMI_r64Position_ChuChe`)     | LREAL    | mm (>0)                                         | 走车（钢领板）一次出行的最大距离                                               | HMI         |
| 出车速度 (`HMI_r64Velocity_ChuChe`)          | LREAL    | mm/s (>0)                                       | 走车出车阶段的目标速度                                                       | HMI         |
| 出车加速度/Jerk (`HMI_r64PositiveAccel_ZouChe_ChuChe`, etc.) | LREAL    | mm/s², mm/s³                                  | 走车出车阶段的动态参数                                                       | HMI         |
| 进车速度/加速度/Jerk (`HMI_r64Velocity_JinChe`, etc.) | LREAL    | mm/s, mm/s², mm/s³                              | 走车进车阶段的动态参数                                                       | HMI         |
| 总牵伸倍数 (`HMI_r64QianShen_All`)           | LREAL    | (>1)                                            | 从输入到输出的总牵伸倍数                                                     | HMI         |
| 分段牵伸倍数/区域 (`HMI_r64QianShen_FenSan`, `HMI_r64QianShen_First`, etc.) | LREAL    |                                                 | 用于多段牵伸的参数                                                         | HMI         |
| 无捻区牵伸 (`HMI_r64QianShen_WuNian`)        | LREAL    | (通常略大于1)                                   | 罗拉与滚筒之间的牵伸比（用于控制滚筒速度）                                       | HMI         |
| 存绕长度 (`HMI_r64_Lcr`)                   | LREAL    | mm (>=0)                                        | 卷绕结束时预留的纱线长度                                                     | HMI         |
| 锭子辅助轴比例因子 (`HMI_r64_DZ_FU_TO_Scale`) | LREAL    |                                                 | 用于锭子辅助轴（时间测量）运动的比例因子                                         | HMI/固定值  |
| 当前卷绕周期 (`g_u16_N_Cycle_Single`)      | UINT     |                                                 | 当前是第几个卷绕周期（层）                                                     | 系统内部    |
| 张力弓/成形弓几何参数 (`HMI_xco`, `HMI_yco`, `HMI_rc0`, etc.) | LREAL    | mm, deg                                         | 张力弓和成形弓的几何尺寸和安装参数                                             | HMI/固定值  |

##### 计算逻辑与核心算法详解

1.  **基础运动参数计算 (`Calculation_0` in `ST_Calculation.txt`)**:
    *   **出车/进车时间**: 根据目标行程、速度、加速度、Jerk，使用标准运动学公式（如 `t = s/v + v/a` 的简化形式或考虑Jerk的更复杂形式）计算。
        *   *伪代码/公式示例*:
            ```st
            // 简化版，实际代码中可能更复杂以考虑Jerk
            Time_ChuChe := Position_ChuChe / Velocity_ChuChe + Velocity_ChuChe / Accel_ChuChe;
            ```
    *   **加捻时间与捻缩参数**:
        *   **目标锭子总转数 (加捻)**: `TotalSpindleRevs_Twist = Position_ChuChe * TargetTwist_TPM * 0.36` (0.36为经验系数或单位转换)。
        *   **锭子速度曲线**: 通常为梯形或S形速度曲线。最大速度由 `HMI_r64NianDu_QianShen_Best` (最佳捻度时的前罗拉速度，间接影响锭子速度) 和机器传动比决定。
        *   **通过锭子辅助轴 (`Axis_DZ_FU`) 实际运行测量时间**:
            1.  根据计算出的锭子总转数和速度曲线，设定 `Axis_DZ_FU` 的目标位置、速度、加速度。
            2.  启动 `Axis_DZ_FU` 运动，并使用 `_getinternaltimestamp()` 获取运动开始 (`RetUDINT_getinternaltimestamp_1`) 和结束 (`RetUDINT_getinternaltimestamp_2`) 的时间戳。
            3.  使用 `_gettimedifferenceofinternaltimestamps()` 计算实际运动时间 (`g_r64Time_Buffer_JiaNian`)。
            4.  类似地，在锭子辅助轴运动到预设的捻缩开始对应位置时，获取时间戳 (`RetUDINT_getinternaltimestamp_NianSuo`)，用于计算到捻缩开始点的时间 (`g_r64Axis_DingZi_Time_NianSuo_Buffer_Start`)。
        *   **捻缩时间/速度计算**: 根据总加捻时间、出车时间、捻缩开始点时间、捻缩量 (`HMI_r64Position_NianSuo`)，计算捻缩段的持续时间和所需的走车速度。

2.  **纱穗成型几何与周期计算 (`FB_V_Sub` in `ST_FB_V_Sub.txt` and its called FCs in `ST_FC.txt`)**:
    *   **输入**: 纱管几何尺寸, 目标纱穗最大直径, 各部分高度, 纱线支数, 卷绕长度, 纱线密度。
    *   **核心算法**:
        1.  **计算单位周期纱线体积增量 (`g_V_u`)**: `g_V_u = g_Ljr / (g_m_u * g_Nm)` (单位需统一)。
        2.  **计算达到最大直径时的理论体积和周期数 (`g_V_D`, `g_ND`)**:
            *   调用 `FC_V_N` 函数，该函数内部通过几何积分公式（将纱穗在达到最大直径时的形状分解为多个旋转体段）计算出理论体积 `V_0_at_max_diameter`。
            *   `g_ND = TRUNC(V_0_at_max_diameter / g_V_u)`。
            *   `g_V_D = g_ND * g_V_u`。
            *   调用 `FC_FROOT_g2` (内部使用 `FC_V_g2` 和二分法)，调整纱穗顶部的几何参数 `g2_D`，使得基于此参数计算出的体积与 `g_V_D` 匹配，实现几何形状的“圆整”。
        3.  **计算满纱时的理论体积和周期数 (`g_V`, `g_N`)**: 逻辑与步骤2类似，但使用的是最终纱穗轮廓的几何参数。
    *   **输出**: `g_ND` (达到最大直径时的周期数), `g_N` (满纱时的总周期数)。

3.  **迭代成型计算 (`Calculation_1` in `ST_Calculation.txt` and its called FCs in `ST_FC.txt`)**:
    *   **输入**: `g_ND`, `g_N`, 纱穗几何参数, 当前卷绕周期 `g_u16_N_Cycle_Single`。
    *   **核心算法 (对每个卷绕周期 `g_u16_N_Cycle_Single` 进行迭代)**:
        1.  **计算当前周期的目标累积纱线体积 (`g_V_n`)**: `g_V_n = g_V_u * (g_u16_N_Cycle_Single + 1)`。
        2.  **求解当前层纱线的几何参数**:
            *   **最大直径前 (`g_u16_N_Cycle_Single < g_ND`)**:
                *   调用 `FC_FROOT_sig_delt_f2_n_D` (内部使用 `FC_V_sig_delt_f2_n_D` 和二分法)，求解一个控制参数 `sig_delt_f2_n`。该参数决定了当前层纱线在纱穗锥面部分的厚度或形状，目标是使形成的体积等于 `g_V_n`。
                *   根据 `sig_delt_f2_n` 计算出当前层纱线的关键几何点 (`g_f1_n_DieDai`, `g_f2_n_DieDai`, `g_g1_n_DieDai`, `g_g2_n_DieDai`) 和斜率 (`g_kFG_n_DieDai`)。
            *   **最大直径后 (`g_ND <= g_u16_N_Cycle_Single < g_N`)**:
                *   斜率 `g_kFG_n_DieDai` 根据预设的从最大直径时的斜率到满纱时斜率的过渡函数计算（例如线性或基于角度的插值）。
                *   调用 `FC_FROOT_f2_n` (内部使用 `FC_V_f2_n` 和二分法)，求解当前层纱线的一个关键y坐标 `f2_n` (通常是平行卷绕段的下边缘或顶部锥区的下边缘)。目标是使形成的体积等于 `g_V_n`。
                *   根据 `f2_n` 和 `g_kFG_n_DieDai` 计算其他几何点。
        3.  **计算导纱弓位置与走车位置的对应关系 (为Cam_1生成数据点)**:
            *   根据当前层纱线的几何形状，将纱穗在垂直方向上划分为底部、中部、顶部三个区域。
            *   通过几何积分（代码中体现为一系列 `g_y_RX_D_n`, `g_y_SX_D_n`, `g_y_TX_D_n` 系数的计算，这些是三次多项式积分的结果），计算出这三个区域分别对应的纱线体积增量 (`g_V_u_Bottom_D_n`, `g_V_u_Middle_D_n`, `g_V_u_Top_D_n`)。
            *   这些体积增量再除以 `g_k_VL_n` (单位走车长度对应的体积增量比例系数)，得到在形成这些体积时，走车需要行进的距离。这间接建立了导纱弓在不同高度（对应不同体积填充区域）与走车行程之间的关系。
        4.  **计算纱穗高度的微小增量 (`g_x_Delta`)**: 根据当前层与上一层几何参数的变化，以及一个调整因子 `g_n_i_Delta` (该因子由 `ST_Clc_V_Sub.txt` 根据工艺参数变化计算得到，用于补偿不同纱线支数等对成型高度的影响)，计算出纱穗在垂直方向上的实际高度增量。这个增量会累加到 `g_x_Sum_Delta`，用于后续凸轮曲线的垂直偏移。

##### 输出参数列表 (部分已在1.1中列出，这里补充迭代相关的)

| 参数名称 (代码参考)                                  | 数据类型 | 单位     | 描述                                                                 |
| :--------------------------------------------------- | :------- | :------- | :------------------------------------------------------------------- |
| 纱穗当前层几何参数 (`g_f1_n_DieDai`, `g_kFG_n_DieDai`, etc.) | LREAL[]  | mm, -    | 迭代计算出的当前卷绕层纱线的关键几何点和斜率                               |
| 导纱弓-走车位置对应关系数据 (`g_Vj_1`, `g_Lj_1`, etc.) | LREAL[]  | mm³, mm  | 用于生成成形凸轮的中间数据                                                 |
| 纱穗高度累计增量 (`g_x_Sum_Delta`)                     | LREAL    | mm       | 考虑了调整因子后的纱穗实际高度累计增量                                     |
| Cam_1 主令程点集 (`g_L_s_DieDai_cam`)                  | ARRAY OF LREAL | mm       | 成形凸轮的主轴（走车）位置序列                                           |
| Cam_1 从动程点集 (`g_r64Axis_ChengXing_Position_JuanRao_cam_DieDai`) | ARRAY OF LREAL | deg      | 成形凸轮的从动轴（成形弓）角度序列                                       |
| Cam_3 主令程点集 (`g_xit_TuiRao_DieDai_real`)          | ARRAY OF LREAL | deg      | 退绕凸轮的主轴（成形弓）角度序列                                         |
| Cam_3 从动程点集 (`g_x_TuiRao_DieDai_real`)            | ARRAY OF LREAL | deg      | 退绕凸轮的从动轴（锭子）位置序列                                         |

##### 支持的工艺类型

*   **环锭纺纱 (推测)**: 核心工艺。
*   **筒子纱/宝塔纱成型**: 通过Cam_1实现。
*   **分段加捻/追捻**: 通过HMI参数和`Calculation_0`中的逻辑支持。
*   **捻缩补偿**: 通过`Calculation_0`中的逻辑支持。
*   **多段牵伸**: 通过HMI参数和`Calculation_1`中对罗拉速比的调整支持。

#### 2.1.2. 凸轮生成

##### 输入参数列表

1.  **Cam_1 (成形凸轮 - `Axis_ChengXing` 从动)**:
    *   主令轴: `Axis_ZouChe` (实际走车轴) 或 `Axis_ZC_FU` (走车虚拟主令轴)。
    *   **数据点序列**: 由 `Calculation_1` 生成的 `g_L_s_DieDai_cam` (主令程) 和 `g_r64Axis_ChengXing_Position_JuanRao_cam_DieDai` (从动程)。
    *   卷绕角度参数 (`HMI_alpha_JuanRao_1`, `HMI_alpha_JuanRao_2`): 用于调整纱线在纱穗表面的缠绕角度，影响绫交叉。
    *   纱穗高度累计增量 (`g_x_Sum_Delta`): 用于从动程的垂直方向偏移。
    *   成形弓机构参数 (`HMI_xco`, `HMI_yco`, `HMI_rc0`, `HMI_xit_0`): 用于将计算出的导纱点y坐标转换为成形弓轴的实际角度位置。

2.  **Cam_3 (退绕凸轮 - `Axis_DingZi` 从动)**:
    *   主令轴: `Axis_ChengXing` (成形弓轴)。
    *   **数据点序列**: 由 `Calculation_1` 生成的 `g_xit_TuiRao_DieDai_real` (主令程) 和 `g_x_TuiRao_DieDai_real` (从动程)。
    *   退绕曲线形状参数 (`HMI_k_TuiRao`, `HMI_n_Change_DieDai_TuiRao`): 用于调整退绕过程中锭子运动速度曲线的形状。
    *   锭子加捻结束位置 (`g_r64Axis_DingZi_ActualPosition_JiaNian_DieDai_End`): 作为退绕的基准点。
    *   凸轮缩放值 (`g_scalevalue_Cam_DieDai_3`): 用于从动程的缩放。

3.  **Cam_2 (张力补偿凸轮 - `Axis_ZL_T_FU` 从动)**:
    *   主令轴: `Axis_1_ZhangLi` (主张力弓轴)。
    *   **数据点序列**: HMI数组 `HMI_r64Axis_1_ZhangLi_Position_CeDing_N` (主令程 - 张力弓位置) 和 `HMI_r64Axis_1_ZhangLi_Torque_CeDing_N` (从动程 - 测定力矩)，由张力测定模式采集。

##### 凸轮轮廓曲线生成算法详解 (`Calculation_2` for Cam_1/Cam_3, `Task_cam2` for Cam_2)

*   **算法类型**: 离散点插补法。
*   **步骤**:
    1.  **复位凸轮对象**: `_resetcam(cam := Cam_X)`。
    2.  **添加数据点**:
        *   对于Cam_1和Cam_3，循环遍历由`Calculation_1`生成的点集，使用 `_addpointtocam` 将 (主令程, 从动程) 点对添加到对应的凸轮对象。
        *   对于Cam_2，循环遍历HMI数组中的测定数据点，使用 `_addpointtocam` 添加。通常会添加一个起始偏移点以确保曲线起点平滑。
    3.  **插补**: `_interpolatecam(cam := Cam_X, interpolationmode := C_SPLINE)`。使用三次样条插值方法在数据点之间生成平滑曲线。
    4.  **缩放与偏移 (可选)**:
        *   `_setcamscale`: 可用于对主令程或从动程进行整体或局部缩放。例如，Cam_1的主令程会根据实际卷绕长度与理论长度的比例进行缩放。Cam_3的从动程可能会根据 `g_scalevalue_Cam_DieDai_3` 进行缩放。
        *   `_setcamoffset` (现有代码中Cam_5有用到，但Cam_1/3/2未见直接使用，但逻辑上可能需要): 可用于对主令程或从动程进行整体偏移。

##### 输出数据格式

*   **Simotion内部凸轮表**: 最终生成的凸轮曲线存储在Simotion的Cam技术对象 (`Cam_1`, `Cam_2`, `Cam_3`) 内部，供同步运动指令调用。

##### 支持的凸轮类型和从动件类型

*   **凸轮类型 (逻辑上)**:
    *   **盘形往复凸轮**: Cam_1 (成形弓) 的运动特性。
    *   **盘形旋转/摆动凸轮**: Cam_3 (锭子退绕) 的运动特性。
    *   **函数发生器/补偿曲线**: Cam_2 (张力补偿) 的特性。
*   **从动件类型 (逻辑上)**:
    *   **成形弓/导纱器**: 往复直线运动或摆动。
    *   **锭子**: 旋转运动。
    *   **张力弓辅助轴**: 其输出可能是力矩或用于调整主张力弓的位置的偏移量。

### 2.2. 非功能需求

*   **性能要求**:
    *   **参数计算时间**: HMI触发的参数重算（如更改纱支），响应时间应在1-2秒内。
    *   **迭代计算与凸轮生成时间**: 在每个卷绕周期变化时触发的重新计算和凸轮生成 (Cam_1, Cam_3)，必须在下一个周期开始前（通常是出车阶段）完成。考虑到机器运行速度，此时间应控制在几十到几百毫秒。
    *   **实时性**: 张力控制相关的实时计算（如锭子速度调整，依赖本模块输出的纱穗直径估算）必须在Simotion的IPO周期（通常为2-8ms）内完成。
*   **精度要求**:
    *   **纱穗成型精度**: 凸轮曲线的计算精度直接影响纱穗形状和密度均匀性。轮廓点计算精度应达到0.01mm或更高（取决于机械精度）。插值算法应保证曲线平滑无突变。
    *   **运动参数精度**: 速度、加速度等参数的计算精度应满足控制要求。
    *   **计算精度**: 所有浮点数运算使用LREAL，注意避免精度累积误差。
*   **可靠性与错误处理机制**:
    *   **参数校验**: 对所有HMI输入的关键工艺参数进行严格的范围和逻辑校验。
    *   **算法鲁棒性**: 数值迭代算法（如二分法）必须有最大迭代次数限制，并能处理不收敛的情况，返回错误码或使用备用值。
    *   **除零等算术错误**: 进行预防。
    *   **故障反馈**: 计算错误或参数异常应能反馈给主控模块和HMI。

## 3. 架构设计

### 3.1. 模块内部组件划分及其职责

```mermaid
graph TD
    subgraph 计算与凸轮模块
        A[输入接口层] -- 工艺参数/触发信号 --> B{参数校验器};
        B -- 合法参数 --> C[核心计算引擎];
        C -- 纱穗几何/周期请求 --> F1[纱穗几何与周期计算器 (FB_V_Sub & FCs)];
        F1 -- 几何/周期结果 --> C;
        C -- 基础运动参数请求 --> F2[基础运动参数计算器 (Calculation_0)];
        F2 -- 运动参数 --> C;
        C -- 迭代成型与凸轮点请求 --> F3[迭代成型与凸轮点生成器 (Calculation_1 & FCs)];
        F3 -- Cam_1/3点集 --> C;
        C -- 凸轮对象配置请求 --> F4[凸轮对象配置器 (Calculation_2)];
        F4 -- Cam_1/3对象 --> G[输出接口层];
        A -- 张力测定数据 --> H[张力补偿凸轮生成器 (Task_cam2)];
        H -- Cam_2对象 --> G;
        C -- 计算结果 --> G;
        G -- 输出参数/凸轮 --> I[其他模块 (主控/轴控/HMI)];
    end
```

*   **输入接口层**: 负责接收来自HMI和主控模块的参数及触发信号。
*   **参数校验器**: 对输入的关键参数进行合法性校验。
*   **核心计算引擎**: 协调内部各计算组件的调用顺序和数据流。
*   **纱穗几何与周期计算器**: 封装`FB_V_Sub`及其调用的`FC_V_N`, `FC_FROOT_g2`等，负责计算`g_ND`, `g_N`。
*   **基础运动参数计算器**: 对应`Calculation_0`，计算时间、速度、捻缩等。
*   **迭代成型与凸轮点生成器**: 对应`Calculation_1`及其调用的众多`FC_FROOT_...`函数，负责逐层纱穗几何计算和Cam_1/Cam_3数据点生成。
*   **凸轮对象配置器**: 对应`Calculation_2`，负责将数据点加载到Simotion Cam对象并进行插补/缩放。
*   **张力补偿凸轮生成器**: 对应`Task_cam2`，负责根据测定数据生成Cam_2。
*   **输出接口层**: 将计算结果和配置好的凸轮对象通过全局变量提供给其他模块。

### 3.2. 主要类/对象设计 (逻辑概念)

*   **`ProcessParameters` (Struct)**: 存储所有输入的工艺参数。
*   **`BobbinGeometryModel` (FB or Group of FCs)**: 封装纱穗几何和体积计算。
    *   `calculateTheoreticalCycles(tubeParams, targetParams, yarnParams)` -> `ND, N`
*   **`MotionPlanner` (FB or Group of FCs)**: 封装基础运动参数计算。
    *   `calculateMotionTimes(travel, speed, accel)` -> `time`
    *   `calculateTwistShrinkParams(...)` -> `shrinkTime, shrinkSpeed`
*   **`LayerFormationEngine` (FB or Program - `Calculation_1`)**: 核心迭代引擎。
    *   `iterateNextLayer(currentCycle, prevLayerGeom, targetVolume)` -> `newLayerGeom, cam1Points, cam3Points`
*   **`CamProfileGenerator` (Program - `Calculation_2`, `Task_cam2`)**:
    *   `generateCam(camObject, pointsArray, interpolationMethod, scaleParams)`

### 3.3. 数据结构设计

*   **`TYPE sBobbinTubeParams : STRUCT ... END_STRUCT`**: 纱管几何参数。
*   **`TYPE sTargetBobbinShape : STRUCT ... END_STRUCT`**: 目标纱穗形状参数。
*   **`TYPE sYarnProperties : STRUCT ... END_STRUCT`**: 纱线特性参数。
*   **`TYPE sMotionDynamicParams : STRUCT ... END_STRUCT`**: 轴的动态参数（速度、加速度、Jerk）。
*   **`TYPE sCamPoint : STRUCT masterPos:LREAL; slavePos:LREAL; END_STRUCT`**: 凸轮点。
*   **`VAR_GLOBAL g_arrCam1Points : ARRAY[0..MAX_CAM_POINTS] OF sCamPoint;`**: 存储Cam_1数据点。
*   **`VAR_GLOBAL g_stCurrentLayerGeometry : STRUCT ... END_STRUCT`**: 存储当前迭代层的几何参数。

### 3.4. 与其他模块的接口定义 (API规格 - 主要通过全局变量)

*   **触发接口**:
    *   `g_bo_Calculation_0_Start: BOOL` (IN): 启动基础运动参数计算。
    *   `g_i16_Calculation_1_ZhunBei_Status: INT` (IN): 控制`Calculation_1`准备阶段。
    *   `g_i16_Calculation_1_DieDai_Status: INT` (IN): 控制`Calculation_1`迭代阶段。
    *   `g_i16_Calculation_2_Cam_3_Status: INT` (IN): 控制`Calculation_2`配置Cam_3。
    *   `g_i16_Calculation_2_Cam_1_Status: INT` (IN): 控制`Calculation_2`配置Cam_1。
    *   `g_i16_Status_Task_cam2: INT` (IN): 控制`Task_cam2`配置Cam_2。
*   **数据输入接口 (部分示例)**:
    *   `HMI_r64ZhiShu_ChengSha: LREAL`
    *   `HMI_r64NianDu_all: LREAL`
    *   `g_u16_N_Cycle_Single: UINT`
*   **数据输出接口 (部分示例)**:
    *   `g_r64Time_Buffer_ChuChe[Date_Temp]: LREAL`
    *   `g_r64Axis_ZouChe_Position_DieDai_ChuChe[N_Next]: LREAL`
    *   `g_ND, g_N: LREAL` (由`FB_V_Sub`输出，供`Calculation_1`使用)
    *   `Cam_1, Cam_2, Cam_3`: Cam (Simotion技术对象，其内部数据由本模块填充)
*   **状态/错误反馈**:
    *   `g_i16_Calculation_0_Status: INT` (内部状态，也可用于外部监控)
    *   `HMI_boAlarm_NianDu_Time: BOOL`

## 4. 算法详解与工艺动作流程

(此部分与上一节的2.1.1 和 2.1.2 内容高度重合，这里不再重复赘述详细的公式和步骤，仅强调流程和关键点)

### 4.1. 工艺参数计算算法

*   **流程**:
    1.  接收HMI工艺参数。
    2.  **(可选，若参数变化)** 调用 `ST_Clc_V_Sub` -> `FB_V_Sub` 重新计算理论周期数 `g_ND`, `g_N` 和调整因子 `g_n_i_Delta`。
    3.  **(HMI启动或特定条件)** `Calculation_0` 执行：
        *   计算出车/进车时间。
        *   通过 `Axis_DZ_FU` 测量加捻时间。
        *   计算捻缩参数。
        *   结果存入双缓冲数组。
    4.  **(每个卷绕周期或HMI参数变化)** `Calculation_1` 执行：
        *   **准备阶段 (`g_i16_Calculation_1_ZhunBei_Status`)**: 初始化纱穗几何模型，计算初始 `g_ND`, `g_N` (如果尚未由`FB_V_Sub`提供)。
        *   **迭代阶段 (`g_i16_Calculation_1_DieDai_Status`)**:
            *   根据当前周期 `g_u16_N_Cycle_Single`，迭代计算当前层纱线的几何形状。
            *   生成Cam_1和Cam_3的原始数据点。
            *   更新纱穗高度累计增量 `g_x_Sum_Delta`。

### 4.2. 凸轮生成算法

*   **流程**:
    1.  **Cam_1 和 Cam_3 (由 `Calculation_2` 处理)**:
        *   等待 `Calculation_1` 生成完当前周期的点集。
        *   触发 `Calculation_2` (通过 `g_i16_Calculation_2_Cam_1_Status` 和 `g_i16_Calculation_2_Cam_3_Status`)。
        *   `Calculation_2` 调用 `_resetcam`, `_addpointtocam` (循环), `_interpolatecam`, `_setcamscale` 来配置 `Cam_1` 和 `Cam_3` 技术对象。
    2.  **Cam_2 (由 `Task_cam2` 处理)**:
        *   通常在张力测定模式完成后被触发 (通过 `g_i16_Status_Task_cam2`)。
        *   `Task_cam2` 从HMI数组读取测定数据点。
        *   调用 `_resetcam`, `_addpointtocam` (循环), `_interpolatecam` 来配置 `Cam_2` 技术对象。

### 4.3. 工艺动作流程 (模块内部工作流)

```mermaid
sequenceDiagram
    participant User/HMI
    participant MainControl
    participant CalcAndCamModule
    participant AxisTOs

    User/HMI->>MainControl: 设定工艺参数 / 发出操作指令
    MainControl->>CalcAndCamModule: 触发计算 (e.g., 设置 g_i16_Calculation_0_Start)
    MainControl->>CalcAndCamModule: 传递HMI参数 / 当前周期数

    CalcAndCamModule->>CalcAndCamModule: **执行 Calculation_0** (基础运动参数)
    Note right of CalcAndCamModule: 计算时间, 捻缩等

    alt 工艺参数变化
        MainControl->>CalcAndCamModule: 触发 FB_V_Sub (通过 ST_Clc_V_Sub)
        CalcAndCamModule->>CalcAndCamModule: **执行 FB_V_Sub** (计算 g_ND, g_N, g_n_i_Delta)
    end

    alt 新周期 或 需要更新凸轮
        MainControl->>CalcAndCamModule: 触发 Calculation_1 (设置状态变量)
        CalcAndCamModule->>CalcAndCamModule: **执行 Calculation_1** (迭代成型, 生成Cam_1/3点集)
        CalcAndCamModule->>MainControl: (可选)反馈计算状态

        MainControl->>CalcAndCamModule: 触发 Calculation_2 (设置状态变量)
        CalcAndCamModule->>CalcAndCamModule: **执行 Calculation_2** (配置Cam_1, Cam_3对象)
        Note right of CalcAndCamModule: _resetcam, _addpointtocam, _interpolatecam, _setcamscale
    end

    alt 张力测定后
        MainControl->>CalcAndCamModule: 触发 Task_cam2 (设置状态变量)
        CalcAndCamModule->>CalcAndCamModule: **执行 Task_cam2** (配置Cam_2对象)
    end

    CalcAndCamModule-->>MainControl: (部分)计算结果 / 状态
    CalcAndCamModule-->>AxisTOs: (通过全局变量)设定运动参数 / 更新凸轮对象
```

## 5. 数据管理

### 5.1. 输入数据的校验规则与处理

*   **HMI参数**: 在模块接收到参数后，应进行范围检查（如直径、速度、捻度不能为负或零）、逻辑一致性检查（如纱穗最大直径应大于纱管直径）。
*   **处理**: 若校验失败，应设置错误标志，并通过全局变量通知主控模块和HMI，阻止使用无效参数进行计算。

### 5.2. 中间数据的存储与管理

*   **全局数组**: 用于存储迭代计算的每层几何参数、凸轮点序列。需注意数组大小应足够，并有防止越界的机制。
*   **双缓冲/循环缓冲**:
    *   `Calculation_0` 的输出参数使用 `Date_Temp`/`Date_Available` 进行双缓冲。
    *   `Calculation_1` 的迭代参数使用 `N_Current`/`N_Before`/`N_Next` 实现循环缓冲，确保上一周期的有效数据可用于当前周期的计算或比较。
*   **RETAIN变量**: 关键的配置型参数或累计值（如 `g_n_Change_D` 及其关联的参数历史数组）应考虑使用 `RETAIN` 存储，以防PLC断电丢失。

### 5.3. 输出数据的格式化与持久化

*   **运动参数**: 直接以LREAL/INT等格式写入全局变量。
*   **凸轮数据**: 存储在Simotion的Cam技术对象中。这些对象的数据通常随项目一起保存。如果需要在运行时动态生成且断电保持，则需要考虑额外的持久化机制（如保存到PLC文件系统或配方）。现有代码似乎是运行时动态生成。

## 6. 错误处理与异常机制

### 6.1. 可预见的错误类型及其处理策略

*   **无效输入参数**: 返回错误代码，设置全局报警标志，HMI提示。
*   **计算溢出/除零**: 在运算前检查除数是否为零，对可能溢出的中间结果进行范围限制或报警。
*   **数值迭代不收敛**: `FC_FROOT_...` 函数应包含最大迭代次数限制。若超时未收敛，函数返回特定错误码或无效值。上层调用者需检查此返回值，并采取相应措施（如使用默认值、报警、停止相关流程）。
*   **Simotion系统函数错误**: 检查 `_addpointtocam`, `_interpolatecam` 等函数的返回值。若非零，记录错误，报警。
*   **数组越界**: 严格控制循环和数组访问的索引。

### 6.2. 日志记录规范

*   **重要参数变化**: 记录HMI关键工艺参数的修改事件和新值。
*   **计算模块启动/结束**: 记录`Calculation_0`, `Calculation_1`, `Calculation_2`, `Task_cam2` 等主要计算程序的启动和结束，以及关键的输入/输出。
*   **迭代过程关键值**: 在`Calculation_1`的迭代中，可选择性记录每隔N个周期的关键几何参数，用于调试和分析。
*   **错误与警告**: 详细记录发生的任何计算错误、参数校验失败、迭代不收敛等情况，包括相关参数值。

## 7. 测试要点

### 7.1. 单元测试的关键测试用例

*   **核心FCs (`FC_V_N`, `FC_FROOT_g2`, `FC_V_sig_delt_f2_n_D`, etc.)**:
    *   使用已知的输入参数和预期的输出结果进行验证。
    *   测试边界条件（如参数为0，最大/最小值）。
    *   测试数值求解函数的收敛性（不同初值、步长）。
*   **`FB_V_Sub`**:
    *   输入不同的纱管/纱穗/纱线参数，验证输出的 `g_ND`, `g_N` 是否符合预期。
*   **`Calculation_0`**:
    *   验证各种运动时间、捻缩参数计算的准确性。
    *   模拟锭子辅助轴运动，验证时间戳测量逻辑。
*   **`Calculation_1` (迭代引擎)**:
    *   **单周期迭代**: 给定特定周期的输入，验证输出的几何参数和凸轮点是否正确。
    *   **完整流程**: 从空管到满纱，验证整个迭代过程的正确性，检查最终生成的凸轮点序列是否平滑、合理。
    *   测试不同工艺参数组合对迭代结果和凸轮形状的影响。
*   **`Calculation_2` / `Task_cam2` (凸轮配置)**:
    *   验证凸轮点是否正确加载到Cam对象。
    *   验证插补和缩放功能是否按预期工作。

### 7.2. 集成测试的考虑

*   **与主控模块的接口**:
    *   测试在不同操作模式（手动、自动、空锭）和状态下，本模块是否被主控模块正确触发（通过设置相应的全局状态变量）。
    *   测试HMI工艺参数修改后，本模块是否能正确接收并重新计算。
*   **与轴运动控制模块的接口**:
    *   将本模块计算出的运动参数（速度、加速度等）和生成的凸轮曲线实际应用到轴的TO程序。
    *   在仿真环境或实际设备上，观察轴的运动轨迹是否符合预期，纱穗成型是否良好，张力是否稳定。
*   **性能测试**:
    *   测量在最坏情况下（如参数最复杂、迭代次数最多），`Calculation_1` 和 `Calculation_2` 的执行时间，确保其满足系统的实时性要求（例如，必须在下一个卷绕周期开始前完成）。
*   **鲁棒性测试**:
    *   输入异常或边界工艺参数，观察模块的错误处理能力和系统的整体稳定性。

## 8. 附录 (可选)

### 8.1. 相关术语解释

*   **纱穗 (Bobbin/Package)**: 纱线在纱管上卷绕形成的整体。
*   **绫交叉 (Traverse/Winding Pattern)**: 纱线在纱穗表面按特定规律交叉排列，以形成稳定且密度均匀的纱穗。
*   **出车 (Delivery/Traverse Out)**: 细纱机走车（钢领板）向外运行，进行牵伸和加捻的过程。
*   **进车 (Return/Traverse In)**: 细纱机走车向内运行，进行卷绕的过程。
*   **捻度 (Twist Per Meter - TPM)**: 单位长度纱线上的捻回数。
*   **牵伸 (Draft)**: 纱条被拉伸变细的过程，以牵伸倍数表示。
*   **捻缩 (Twist Contraction)**: 纱线加捻后因纤维收缩引起的长度缩短。
*   **生头 (Piecing/End Finding)**: 在空纱管上开始卷绕或断头后再接头的过程。
*   **存绕 (Reserve Winding/Tail Winding)**: 在卷绕结束时，额外卷绕几圈以固定纱尾。
*   **IPO (Interpolator)**: Simotion运动控制器中的插补器，负责根据设定点生成平滑的运动轨迹。
*   **TPM**: Twists Per Meter (每米捻回数)。
*   **Nm**: Metric count (公制支数)，衡量纱线细度的单位。

### 8.2. 参考文献

*   (可列出相关的纺织工程书籍、Simotion编程手册、运动控制理论文献、凸轮设计手册等)