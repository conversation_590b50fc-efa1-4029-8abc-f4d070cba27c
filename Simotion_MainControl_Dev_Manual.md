# Simotion纺织机械 - 主控制与状态管理模块开发说明书

## 1. 模块概述

主控制与状态管理模块（后续简称“主控模块”）是整个纺织机械控制系统的核心调度单元。它负责解析操作员指令（通过HMI），监控设备状态，并根据预设的工艺流程和操作模式，协调其他所有功能模块（如参数计算、轴运动控制、张力控制等）的有序执行。

该模块的核心是一个复杂的多层状态机，用于管理机器从上电、初始化、手动操作、自动生头到全自动循环生产的整个生命周期。

**主要职责：**

*   **模式管理**: 实现并管理系统的主要操作模式及其之间的切换逻辑。
*   **状态监控**: 监控关键的系统状态（如轴就绪、电源模块状态、故障报警等）。
*   **流程控制**: 根据当前模式和状态，按预定顺序触发和协调各个子任务和运动序列。
*   **HMI交互**: 响应HMI的操作指令，并向HMI提供必要的系统状态和数据显示。
*   **参数传递**: 将HMI设定的工艺参数或内部计算得到的参数传递给相应的运动控制模块或计算模块。
*   **故障处理**: 在检测到故障时，能够将系统切换到安全的停止状态，并提示报警。

## 2. 核心状态机设计

系统运行的核心由一个主状态变量 `g_i16_Mode_enabled` 和一个模式选择变量 `g_i16_Mode_Selected` 控制。此外，在自动运行模式 (`_Automatic`) 和空锭生头模式 (`_KongDing`) 下，还存在更细分的子状态机。

### 2.1. 主要操作模式 (`g_i16_Mode_enabled`)

以下是基于现有代码分析出的主要操作模式及其常量值（推测）：

*   `_Error` (例如: 1): 系统错误状态。
*   `_Disable` (例如: 2): 系统禁止/急停状态。所有轴应停止并抱闸（如果配备）。
*   `_Manual` (例如: 4): 手动操作模式。允许操作员通过HMI点动控制各个轴。
*   `_Startposition` (例如: 8): 回原位/准备模式。将各轴移动到生产开始前的预设准备位置。
*   `_Automatic` (例如: 16): 自动运行模式。执行连续的生产循环。
*   `_KongDing` (例如: 512): 空锭生头/落纱模式。执行特定的自动化序列以准备新的生产批次。
*   `_ZhangLi_CeDing` (例如: 256): 张力测定模式。用于标定张力系统。
*   `_Nothing_Selected` (例如: 0): 无模式选定或模式执行完毕的中间状态。

**模式切换逻辑**:

*   通常由HMI按钮触发，设置 `g_i16_Mode_Selected` 为目标模式。
*   主控模块检测到 `g_i16_Mode_Selected` 变化后，在满足特定条件（如当前模式允许切换、无严重故障等）的情况下，将 `g_i16_Mode_enabled` 更新为新的模式，并复位 `g_i16_Mode_Selected`。
*   模式切换时，可能需要执行一些进入/退出当前模式的特定操作（如停止当前运动、复位状态变量等）。

### 2.2. 自动运行模式 (`_Automatic`) 子状态 (`g_i16_automatic_status`)

当 `g_i16_Mode_enabled = _Automatic` 时，系统进入自动生产循环，其流程由子状态变量 `g_i16_automatic_status` 控制：

*   `_Waiting` (例如: 1): 等待操作员按下“自动启动”按钮。
    *   **动作**: 启动前响铃提示。
    *   **转换**: 接收到启动指令后，根据上次停止的位置或是否为首次启动，跳转到相应的生产阶段（通常是 `_ChuChe`）。
*   `_MergeStop` (例如: 2): 紧急/故障停止。
    *   **动作**: 立即以最大减速度停止所有相关轴的运动。
    *   **转换**: 通常需要手动复位故障并重新选择操作模式。
*   `_AutoStop` (例如: 4): 自动停止（满纱或达到设定产量）。
    *   **动作**: 执行一个受控的停车序列，将各轴移动到预设的自动停止位置。通常会进行一些准备落纱的动作。
    *   **转换**: 停止后，通常等待操作员指令进入 `_KongDing` (落纱) 或其他模式。
*   `_ChuChe` (例如: 8): 出车阶段。
    *   **工艺动作**: 走车轴向外运行，罗拉轴和滚筒轴按设定的牵伸比与走车（或其虚拟主令）同步送出纤维条，锭子轴高速旋转进行加捻。张力弓和成形弓协同运动以控制纱线路径和张力。
    *   **核心控制**:
        *   走车轴执行定位到满行程。
        *   罗拉轴与走车虚拟主令 (`Axis_ZC_FU`) 进行位置齿轮同步（牵伸比 `g_r64_Gearing_Ratio_LuoLa`）。
        *   滚筒轴与罗la轴进行速度齿轮同步（速比 `1.0 / HMI_r64QianShen_WuNian`）。
        *   锭子轴执行定位到总加捻圈数，速度分段（一捻、二捻）。
        *   张力弓轴根据张力反馈或预设曲线进行位置调整或力矩控制。
        *   成形弓轴通常在此阶段保持在较高位置或按特定轨迹小幅运动。
    *   **转换**: 出车到设定位置后，进入 `_ChuCheDao` 阶段。
*   `_ChuChe_Middle` (例如: 新增状态): 出车到中间位置暂停。
    *   **工艺动作**: 与 `_ChuChe` 类似，但目标位置为中间某点。
    *   **转换**: 等待操作员触发继续，然后可能进入 `_ChuChe_Wait` 或直接进入 `_ChuChe` 的剩余部分。
*   `_ChuChe_Wait` (例如: 新增状态): 等待从中间位置继续的触发。
*   `_ChuCheDao` (例如: 16): 出车到点 / 捻缩阶段。
    *   **工艺动作**: 走车继续缓慢向外移动（或保持在最外端），锭子完成剩余加捻。如果启用了捻缩，走车轴会根据锭子轴的位置（捻度）反馈，以一个较慢的速度反向运动一小段距离，以补偿加捻引起的纱线收缩。
    *   **核心控制**:
        *   锭子轴完成定位。
        *   走车轴根据捻缩参数（捻缩量 `g_r64Position_DieDai_NianSuo`，捻缩速度 `g_r64Axis_ZouChe_Velocity_DieDai_NianSuo`）执行反向定位或速度运动。
    *   **转换**: 完成后，进入 `_TuiRao` 阶段。
*   `_TuiRao` (例如: 32): 退绕阶段。
    *   **工艺动作**: 锭子轴反向旋转，将纱线从纱穗上退绕下来，同时成形弓和张力弓协同运动，引导纱线路径，为后续卷绕做准备。走车轴通常保持在最外端或缓慢内移。
    *   **核心控制**:
        *   锭子轴与成形弓轴进行凸轮同步 (`Cam_3`)，主令为成形弓，从动为锭子。
        *   成形弓轴执行从纱穗顶部到底部的定位运动。
        *   张力弓轴可能执行与成形弓联动的定位运动。
    *   **转换**: 退绕完成后，进入 `_JuanRao` 阶段。
*   `_JuanRao` (例如: 64): 卷绕/进车阶段。
    *   **工艺动作**: 走车轴向内运行，锭子轴正向旋转将纱线卷绕到纱管上，成形弓上下往复运动以实现纱线的绫交叉排列，张力弓动态调整以控制卷绕张力。
    *   **核心控制**:
        *   走车轴执行定位到存绕点附近。
        *   成形弓轴与走车轴进行凸轮同步 (`Cam_1`)，实现绫交叉运动。
        *   锭子轴的速度根据走车速度、当前卷绕直径（通过成形弓位置和纱穗模型估算）以及张力反馈动态调整（参见 `ST_MotionTask_DingZi_2.txt` 逻辑）。
        *   张力弓轴根据张力反馈或预设曲线进行位置调整或力矩控制。
    *   **转换**: 走车到达存绕点附近后，进入 `_CunRao` 阶段。
*   `_CunRao` (例如: 128): 存绕阶段。
    *   **工艺动作**: 走车轴定位到最终的内侧停止位置（原点附近）。锭子继续旋转几圈以固定纱尾。成形弓和张力弓移动到下次出车前的起始位置。
    *   **核心控制**: 各轴执行独立的定位运动。
    *   **转换**: 存绕完成后，如果未达到总产量，则累加产量，当前卷绕周期 `g_u16_N_Cycle_Single` 加1，并重新触发参数计算模块（`Calculation_1`, `Calculation_2`）为下一个周期准备数据，然后状态返回 `_Waiting` 或直接进入 `_ChuChe`。如果达到总产量，则进入 `_AutoStop` 状态。

### 2.3. 空锭生头/落纱模式 (`_KongDing`) 子状态 (`g_i16_KongDing_Status`)

当 `g_i16_Mode_enabled = _KongDing` 时，系统执行一系列复杂的自动化或半自动化步骤，由 `g_i16_KongDing_Status` 控制。此模式根据 `HMI_i16_ShengTou_Select` 的不同选项（如空锭先插管、空锭光锭、落纱）会有不同的流程分支。

**主要步骤（以空锭生头为例，具体细节非常繁多，这里只列举关键流程）：**

1.  **准备与初始化**: 重定义相关轴（滚筒、莱卡、罗拉、锭子）的位置为零。
2.  **选择性出车**: 根据HMI选项，可能先将走车移动一小段距离（如300mm）。
3.  **罗拉送条与成形弓定位**: 罗拉轴送出一段长度的纤维条，同时成形弓移动到纱管顶尖附近。
4.  **罗拉送条与锭子卷绕**: 罗拉轴继续送条，锭子轴开始旋转，将纤维条初步缠绕到纱管上。
5.  **双弓复位**: 成形弓和张力弓移动到各自的起始位置。
6.  **锭子加捻**: 锭子轴旋转一定圈数，对初步缠绕的纱线进行加捻。
7.  **（可选）特殊动作序列 (如状态65)**: 包含负牵伸出车、锭子反转、成形弓/张力弓精确定位、回车卷绕、锭子正转等一系列复杂动作，用于处理特殊情况或优化生头质量。
8.  **主牵伸与加捻 (状态7)**: 走车、罗拉、滚筒、锭子联动，进行一段正式的牵伸和加捻。
9.  **张力弓与成形弓定位 (状态8)**: 张力弓下降，走车和成形弓联动，将成形弓移动到纱管底部。
10. **底部绕线 (状态9)**: 走车和锭子联动，在纱管底部缠绕几圈以固定纱线。
11. **手工处理提示 (状态10)**: 等待操作员进行手工操作（如剥掉毛头）。
12. **成形弓至卷绕点 (状态11)**: 成形弓移动到正常卷绕的起始位置。
13. **（可选）拔锭脚纱序列 (状态111-115)**: 如果选择拔锭脚纱，则执行特定的一系列动作。
14. **进车卷绕至绕头点 (状态12)**: 各轴联动，将纱线从纱管底部卷绕到正常的绕头点，此过程包含动态调整锭子速度。
15. **进车卷绕至存绕点 (状态13)**: 继续卷绕至存绕点。
16. **存绕 (状态14)**: 执行存绕动作，完成后，初始化下一个生产周期的参数，并将模式切换到自动运行 (`_Automatic`) 的 `_ChuChe` 阶段。

**开发说明**:

*   **状态变量**: 使用整数类型变量作为状态标志。建议在开发时使用枚举类型 (ENUM) 来定义状态名，以提高代码的可读性和可维护性。
*   **状态转换图**: 强烈建议为每个状态机（主模式、自动模式子状态、空锭模式子状态）绘制详细的状态转换图，清晰标明每个状态的入口条件、出口条件、状态内执行的动作以及状态间的转换逻辑。
*   **模块化实现**: 每个状态内的具体动作（如特定轴的定位、同步启动等）应尽可能调用标准化的轴运动控制接口模块（TO程序）。
*   **参数化**: 状态转换的条件、运动的目标值、延时时间等应尽可能参数化，通过全局变量或HMI参数进行配置。
*   **错误与异常处理**: 在每个状态和转换逻辑中，考虑可能发生的错误和异常情况，并设计相应的处理机制（如跳转到 `_Error` 或 `_MergeStop` 状态）。
*   **时序协调**: 对于多轴联动的复杂序列（尤其在 `_KongDing` 模式中），需要仔细设计各轴运动的时序和协调，确保动作的平稳和精确。使用定时器 (`TON`, `TOF`) 和边沿检测 (`R_TRIG`, `F_TRIG`) 来控制时序。
*   **HMI交互**: 清晰定义HMI按钮（如模式选择、启动、停止、复位、参数设置等）与状态机之间的交互逻辑。HMI应能实时显示当前的主要模式和子状态。

## 4. 算法说明（主控模块层面）

主控模块本身不直接执行复杂的数学算法，但它依赖并触发其他模块的算法执行：

*   **周期计数与触发**:
    *   `g_u16_N_Cycle_Single`: 当前生产批次的卷绕周期计数。
    *   `g_u32_N_Cycle_Whole`: 总的卷绕周期计数。
    *   当 `g_u16_N_Cycle_Single` 发生变化时，会通过 `g_bo_N_Cycle_Changed` 标志和一系列延时脉冲，触发参数计算模块 (`Calculation_1`, `Calculation_2`) 重新计算和生成当前周期的凸轮曲线和相关参数。这是实现动态调整和自适应控制的关键。
*   **牵伸比动态调整**:
    *   在自动运行时，根据当前卷绕周期 `g_u16_N_Cycle_Single` 和HMI设定的分段点及调整比例，动态计算牵伸调整系数 `HMI_r64_scaleValue`。这个系数会传递给参数计算模块，用于调整实际的牵伸比，以适应纱线在纱穗上直径的变化。
*   **模式切换的条件判断**:
    *   例如，从手动模式切换到自动模式前，会检查是否所有轴都已准备就绪，是否处于安全状态等。
    *   从自动运行的某个阶段（如存绕完成）切换到下一个阶段（如出车）或结束（如自动停止），依赖于当前阶段运动的完成状态和产量计数。

## 5. 工艺动作流程（高级概述）

以下是基于状态机分析的主要工艺动作流程的高级概述：

1.  **上电与初始化**:
    *   PLC启动，执行 `st_Startup` 程序，初始化全局标志。
    *   `system_status` 程序监控轴状态和故障。
    *   `st_EnableInfeed` 程序管理电源模块上电。
    *   `Axis_Enable` 程序根据条件使能各个轴。
    *   系统通常进入 `_Manual` 模式或等待操作员选择模式。

2.  **手动操作 (`_Manual`)**:
    *   操作员通过HMI点动控制各个轴（走车、成形弓、张力弓、锭子、罗拉/滚筒）进行调整或准备。
    *   可从此模式跳转到回原位、空锭生头或自动运行模式。

3.  **回原位/准备 (`_Startposition`)**:
    *   各主要轴（走车、成形弓、张力弓）定位到预设的生产准备位置。
    *   对滚筒、莱卡、罗拉、锭子等轴进行位置重定义，为后续同步运动建立基准。
    *   完成后通常返回手动模式，等待下一步指令。

4.  **空锭生头/落纱 (`_KongDing`)**:
    *   这是一个多步骤的自动化序列，用于在空纱管上开始卷绕或处理落纱后的工序。
    *   **核心动作包括**: 轴的精确定位、送条、初步卷绕、加捻、成形弓和张力弓的协同运动、可能的锭子反转和正转等。
    *   **流程分支**: 根据HMI选择（空锭插管、空锭光锭、落纱、是否拔锭脚纱等）执行不同的子序列。
    *   **最终目标**: 完成生头并使机器准备好进入全自动生产循环。完成后通常跳转到自动模式的 `_ChuChe` 阶段。

5.  **自动运行 (`_Automatic`)**:
    *   **出车 (`_ChuChe`)**: 走车外行，进行牵伸和高速加捻。
    *   **捻缩 (`_ChuCheDao`)**: 走车到达外端，完成加捻，并可能进行捻缩补偿。
    *   **退绕 (`_TuiRao`)**: 锭子反绕，成形弓引导纱线退回到起始位置。
    *   **卷绕 (`_JuanRao`)**: 走车内行，锭子正转，成形弓按凸轮曲线上下运动，将纱线均匀卷绕到纱管上，形成纱穗。张力弓动态调整以控制张力。
    *   **存绕 (`_CunRao`)**: 卷绕结束，固定纱尾，各轴复位。
    *   **循环**: 如果未满纱，则重复出车到存绕的过程。
    *   **自动停止 (`_AutoStop`)**: 满纱或达到设定产量后，执行受控停车。

6.  **张力测定 (`_ZhangLi_CeDing`)**:
    *   张力弓按预设点位逐步移动。
    *   在每个点位记录实际力矩。
    *   数据用于生成张力补偿凸轮 (`Cam_2`)。

7.  **故障与急停 (`_MergeStop`, `_Disable`)**:
    *   发生故障或按下急停时，系统进入相应的停止模式，所有运动被安全中止。

## 6. 开发与调试建议

*   **分步实现与测试**: 鉴于系统的复杂性，建议采用分步实现和测试的策略。
    1.  首先实现基础的轴手动控制和状态显示。
    2.  然后实现回原位功能。
    3.  接着开发自动运行模式的各个子阶段，先实现单个阶段的运动，再逐步集成整个循环。可以先不加入复杂的张力闭环和动态凸轮，使用简化的开环控制或固定参数进行测试。
    4.  之后再逐步加入张力控制算法和动态凸轮生成与切换逻辑。
    5.  空锭生头/落纱模式由于步骤繁多且精细，可以放到较后阶段开发。
*   **仿真**: 尽可能利用Simotion Scout的仿真功能进行早期调试，验证状态机逻辑和基本运动序列。
*   **HMI配合**: 开发过程中，HMI界面应能清晰显示当前的模式、子状态、关键变量值，并提供必要的手动干预和参数调整功能，以辅助调试。
*   **日志与追踪**: 在关键的状态转换点和逻辑判断处添加日志输出或追踪变量，方便问题定位。
*   **版本控制**: 使用版本控制系统（如Git）管理代码。

这份针对主控模块的说明书提供了更深层次的逻辑和流程描述。在实际开发中，还需要结合具体的硬件平台、传感器配置以及更细致的工艺要求来不断完善和调整。